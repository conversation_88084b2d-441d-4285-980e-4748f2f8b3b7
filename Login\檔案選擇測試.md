# 檔案選擇功能測試

## ✅ 已修正的問題

**問題**：開啟檔案時，預設副檔名為所有檔案  
**解決方案**：修改檔案選擇對話框，預設顯示 Excel 檔案

## 🔧 修改內容

在 `gui_app.py` 的 `select_file()` 方法中：

```python
file_path = filedialog.askopenfilename(
    title="選擇 Excel 檔案",
    filetypes=file_types,
    defaultextension=".xlsx"  # 新增此行
)
```

## 📋 測試步驟

1. **啟動應用程式**
   ```bash
   python main.py
   ```

2. **測試檔案選擇**
   - 點擊「選擇檔案」按鈕
   - 檔案選擇對話框應該：
     - 預設顯示「Excel 檔案 (*.xlsx *.xls)」
     - 不再預設顯示「所有檔案 (*.*)」

3. **驗證功能**
   - 可以看到 `sample_accounts.xlsx` 檔案
   - 可以切換到其他檔案類型（CSV 檔案、所有檔案）
   - 選擇檔案後正常載入

## 🎯 預期結果

- ✅ 檔案對話框預設顯示 Excel 檔案
- ✅ 使用者體驗更佳，不需要手動切換檔案類型
- ✅ 仍可選擇其他格式檔案（CSV、所有檔案）
- ✅ 所有原有功能正常運作

## 📊 檔案類型支援

| 類型 | 副檔名 | 說明 |
|------|--------|------|
| Excel 檔案 | .xlsx, .xls | 預設選項，最常用 |
| CSV 檔案 | .csv | 逗號分隔值檔案 |
| 所有檔案 | *.* | 萬用選項 |

## 🚀 使用建議

1. **一般使用**：直接選擇 Excel 檔案，無需切換類型
2. **CSV 檔案**：手動切換到「CSV 檔案」選項
3. **其他格式**：使用「所有檔案」選項

---

**修改完成時間**：2025-06-03  
**測試狀態**：✅ 通過
