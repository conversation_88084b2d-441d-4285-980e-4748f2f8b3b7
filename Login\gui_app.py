"""
GUI 應用程式主介面
使用 tkinter 建立圖形化使用者介面
"""
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import json
from datetime import datetime

from excel_processor import ExcelProcessor
from api_client import LoginAPIClient


class LoginGUIApp:
    """登入 GUI 應用程式類別"""
    
    def __init__(self, root: tk.Tk):
        """
        初始化 GUI 應用程式
        
        Args:
            root: tkinter 根視窗
        """
        self.root = root
        self.excel_processor = ExcelProcessor()
        self.api_client = LoginAPIClient()
        
        # 應用程式狀態
        self.current_file_path = None
        self.current_dataframe = None
        self.selected_column = None
        self.accounts_list = []
        self.is_processing = False
        
        self.setup_ui()
        
    def setup_ui(self):
        """設定使用者介面"""
        self.root.title("登入 API 測試工具")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # 建立主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 設定網格權重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 檔案選擇區域
        self.setup_file_section(main_frame)
        
        # 欄位選擇區域
        self.setup_column_section(main_frame)
        
        # 帳號預覽區域
        self.setup_preview_section(main_frame)
        
        # API 設定區域
        self.setup_api_section(main_frame)
        
        # 執行控制區域
        self.setup_control_section(main_frame)
        
        # 結果顯示區域
        self.setup_result_section(main_frame)
        
        # 狀態列
        self.setup_status_bar()
        
    def setup_file_section(self, parent):
        """設定檔案選擇區域"""
        # 檔案選擇標籤框
        file_frame = ttk.LabelFrame(parent, text="Excel 檔案選擇", padding="5")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        file_frame.columnconfigure(1, weight=1)
        
        # 檔案路徑顯示
        ttk.Label(file_frame, text="檔案路徑:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.file_path_var = tk.StringVar(value="尚未選擇檔案")
        self.file_path_label = ttk.Label(file_frame, textvariable=self.file_path_var, 
                                        relief="sunken", background="white")
        self.file_path_label.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        
        # 選擇檔案按鈕
        self.select_file_btn = ttk.Button(file_frame, text="選擇檔案", command=self.select_file)
        self.select_file_btn.grid(row=0, column=2, padx=(5, 0))
        
        # 檔案資訊顯示
        self.file_info_var = tk.StringVar(value="")
        self.file_info_label = ttk.Label(file_frame, textvariable=self.file_info_var, 
                                        foreground="blue")
        self.file_info_label.grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))
        
    def setup_column_section(self, parent):
        """設定欄位選擇區域"""
        # 欄位選擇標籤框
        column_frame = ttk.LabelFrame(parent, text="欄位選擇", padding="5")
        column_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        column_frame.columnconfigure(1, weight=1)
        
        # 工作表選擇
        ttk.Label(column_frame, text="工作表:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.sheet_var = tk.StringVar()
        self.sheet_combo = ttk.Combobox(column_frame, textvariable=self.sheet_var, 
                                       state="readonly", width=20)
        self.sheet_combo.grid(row=0, column=1, sticky=tk.W, padx=(0, 5))
        self.sheet_combo.bind('<<ComboboxSelected>>', self.on_sheet_selected)
        
        # 欄位選擇
        ttk.Label(column_frame, text="帳號欄位:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.column_var = tk.StringVar()
        self.column_combo = ttk.Combobox(column_frame, textvariable=self.column_var, 
                                        state="readonly", width=30)
        self.column_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 5), pady=(5, 0))
        self.column_combo.bind('<<ComboboxSelected>>', self.on_column_selected)
        
    def setup_preview_section(self, parent):
        """設定帳號預覽區域"""
        # 預覽標籤框
        preview_frame = ttk.LabelFrame(parent, text="帳號預覽", padding="5")
        preview_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(1, weight=1)
        
        # 預覽資訊
        self.preview_info_var = tk.StringVar(value="請選擇檔案和欄位")
        self.preview_info_label = ttk.Label(preview_frame, textvariable=self.preview_info_var)
        self.preview_info_label.grid(row=0, column=0, sticky=tk.W)
        
        # 預覽列表
        self.preview_listbox = tk.Listbox(preview_frame, height=6)
        self.preview_listbox.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 預覽列表滾動條
        preview_scrollbar = ttk.Scrollbar(preview_frame, orient="vertical", 
                                         command=self.preview_listbox.yview)
        preview_scrollbar.grid(row=1, column=1, sticky=(tk.N, tk.S))
        self.preview_listbox.configure(yscrollcommand=preview_scrollbar.set)
        
    def setup_api_section(self, parent):
        """設定 API 設定區域"""
        # API 設定標籤框
        api_frame = ttk.LabelFrame(parent, text="API 設定", padding="5")
        api_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        api_frame.columnconfigure(1, weight=1)
        
        # API URL
        ttk.Label(api_frame, text="API URL:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.api_url_var = tk.StringVar(value="http://common:8080")
        self.api_url_entry = ttk.Entry(api_frame, textvariable=self.api_url_var)
        self.api_url_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        
        # 超時設定
        ttk.Label(api_frame, text="超時時間(秒):").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.timeout_var = tk.StringVar(value="30")
        self.timeout_entry = ttk.Entry(api_frame, textvariable=self.timeout_var, width=10)
        self.timeout_entry.grid(row=1, column=1, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        
    def setup_control_section(self, parent):
        """設定執行控制區域"""
        # 控制按鈕框
        control_frame = ttk.Frame(parent)
        control_frame.grid(row=4, column=0, columnspan=2, pady=(0, 10))
        
        # 執行按鈕
        self.execute_btn = ttk.Button(control_frame, text="執行 API 請求", 
                                     command=self.execute_api_requests, state="disabled")
        self.execute_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 停止按鈕
        self.stop_btn = ttk.Button(control_frame, text="停止", 
                                  command=self.stop_execution, state="disabled")
        self.stop_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 清除結果按鈕
        self.clear_btn = ttk.Button(control_frame, text="清除結果", 
                                   command=self.clear_results)
        self.clear_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 匯出結果按鈕
        self.export_btn = ttk.Button(control_frame, text="匯出結果", 
                                    command=self.export_results, state="disabled")
        self.export_btn.pack(side=tk.LEFT)
        
    def setup_result_section(self, parent):
        """設定結果顯示區域"""
        # 結果標籤框
        result_frame = ttk.LabelFrame(parent, text="執行結果", padding="5")
        result_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
        
        # 結果文字區域
        self.result_text = scrolledtext.ScrolledText(result_frame, height=15, wrap=tk.WORD)
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 設定網格權重
        parent.rowconfigure(5, weight=1)
        
    def setup_status_bar(self):
        """設定狀態列"""
        self.status_var = tk.StringVar(value="就緒")
        self.status_bar = ttk.Label(self.root, textvariable=self.status_var, 
                                   relief="sunken", anchor="w")
        self.status_bar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
    def select_file(self):
        """選擇 Excel 檔案"""
        file_types = [
            ("Excel 檔案", "*.xlsx *.xls"),
            ("CSV 檔案", "*.csv"),
            ("所有檔案", "*.*")
        ]

        file_path = filedialog.askopenfilename(
            title="選擇 Excel 檔案",
            filetypes=file_types,
            defaultextension=".xlsx"
        )
        
        if file_path:
            self.load_file(file_path)
            
    def load_file(self, file_path: str):
        """載入檔案"""
        try:
            self.status_var.set("載入檔案中...")
            self.root.update()
            
            # 取得檔案資訊
            file_info = self.excel_processor.get_file_info(file_path)
            if not file_info:
                messagebox.showerror("錯誤", "無法讀取檔案，請檢查檔案格式是否正確")
                return
            
            # 更新介面
            self.current_file_path = file_path
            self.file_path_var.set(file_path)
            
            # 顯示檔案資訊
            info_text = f"檔案: {file_info['file_name']} | 行數: {file_info['rows']} | 欄位數: {file_info['columns']}"
            self.file_info_var.set(info_text)
            
            # 更新工作表選項
            self.sheet_combo['values'] = file_info['sheet_names']
            if file_info['sheet_names']:
                self.sheet_var.set(file_info['sheet_names'][0])
                self.load_sheet(file_info['sheet_names'][0])
            
            self.status_var.set("檔案載入完成")
            
        except Exception as e:
            messagebox.showerror("錯誤", f"載入檔案時發生錯誤: {str(e)}")
            self.status_var.set("載入檔案失敗")
            
    def on_sheet_selected(self, event=None):
        """工作表選擇事件"""
        _ = event  # 忽略事件參數
        sheet_name = self.sheet_var.get()
        if sheet_name:
            self.load_sheet(sheet_name)
            
    def load_sheet(self, sheet_name: str):
        """載入工作表"""
        try:
            # 讀取指定工作表
            if sheet_name == 'CSV':
                self.current_dataframe = self.excel_processor.read_excel_file(self.current_file_path)
            else:
                self.current_dataframe = self.excel_processor.read_excel_file(
                    self.current_file_path, sheet_name)
            
            if self.current_dataframe is not None:
                # 更新欄位選項
                columns = list(self.current_dataframe.columns)
                self.column_combo['values'] = columns
                if columns:
                    self.column_var.set(columns[0])
                    self.on_column_selected()
                    
        except Exception as e:
            messagebox.showerror("錯誤", f"載入工作表時發生錯誤: {str(e)}")
            
    def on_column_selected(self, event=None):
        """欄位選擇事件"""
        _ = event  # 忽略事件參數
        column_name = self.column_var.get()
        if column_name and self.current_dataframe is not None:
            self.update_preview(column_name)
            
    def update_preview(self, column_name: str):
        """更新帳號預覽"""
        try:
            # 提取帳號列表
            self.accounts_list = self.excel_processor.extract_accounts_from_column(
                self.current_dataframe, column_name)
            
            # 更新預覽資訊
            self.preview_info_var.set(f"找到 {len(self.accounts_list)} 個帳號")
            
            # 更新預覽列表
            self.preview_listbox.delete(0, tk.END)
            for account in self.accounts_list[:50]:  # 只顯示前50個
                self.preview_listbox.insert(tk.END, account)
            
            if len(self.accounts_list) > 50:
                self.preview_listbox.insert(tk.END, "... (還有更多)")
            
            # 啟用執行按鈕
            if self.accounts_list:
                self.execute_btn.config(state="normal")
            else:
                self.execute_btn.config(state="disabled")
                
        except Exception as e:
            messagebox.showerror("錯誤", f"更新預覽時發生錯誤: {str(e)}")
            
    def execute_api_requests(self):
        """執行 API 請求"""
        if not self.accounts_list:
            messagebox.showwarning("警告", "沒有可用的帳號資料")
            return
        
        # 確認執行
        result = messagebox.askyesno("確認", 
                                   f"即將對 {len(self.accounts_list)} 個帳號執行 API 請求，是否繼續？")
        if not result:
            return
        
        # 更新 API 客戶端設定
        api_url = self.api_url_var.get().strip()
        if api_url:
            self.api_client.base_url = api_url
        
        # 在新執行緒中執行請求
        self.is_processing = True
        self.execute_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        
        thread = threading.Thread(target=self._execute_requests_thread)
        thread.daemon = True
        thread.start()
        
    def _execute_requests_thread(self):
        """在背景執行緒中執行 API 請求"""
        try:
            timeout = int(self.timeout_var.get())
        except ValueError:
            timeout = 30
        
        self.result_text.delete(1.0, tk.END)
        self.result_text.insert(tk.END, f"開始執行 API 請求...\n")
        self.result_text.insert(tk.END, f"總計帳號數: {len(self.accounts_list)}\n")
        self.result_text.insert(tk.END, f"API URL: {self.api_client.base_url}\n")
        self.result_text.insert(tk.END, f"超時時間: {timeout} 秒\n")
        self.result_text.insert(tk.END, "-" * 50 + "\n\n")
        self.result_text.see(tk.END)
        
        success_count = 0
        error_count = 0
        results = []
        
        for i, account in enumerate(self.accounts_list, 1):
            if not self.is_processing:
                break
                
            self.status_var.set(f"處理中... ({i}/{len(self.accounts_list)})")
            self.root.update()
            
            # 發送 API 請求
            result = self.api_client.send_login_request(account, timeout)
            results.append(result)
            
            # 更新結果顯示
            if result['success']:
                success_count += 1
                self.result_text.insert(tk.END, f"✓ [{i}] {account} - 成功\n")
            else:
                error_count += 1
                self.result_text.insert(tk.END, f"✗ [{i}] {account} - 失敗: {result['error']}\n")
            
            self.result_text.see(tk.END)
            self.root.update()
        
        # 顯示總結
        self.result_text.insert(tk.END, "\n" + "=" * 50 + "\n")
        self.result_text.insert(tk.END, f"執行完成！\n")
        self.result_text.insert(tk.END, f"成功: {success_count} 個\n")
        self.result_text.insert(tk.END, f"失敗: {error_count} 個\n")
        self.result_text.insert(tk.END, f"總計: {len(self.accounts_list)} 個\n")
        self.result_text.see(tk.END)
        
        # 儲存結果供匯出使用
        self.last_results = results
        
        # 重置介面狀態
        self.is_processing = False
        self.execute_btn.config(state="normal")
        self.stop_btn.config(state="disabled")
        self.export_btn.config(state="normal")
        self.status_var.set("執行完成")
        
    def stop_execution(self):
        """停止執行"""
        self.is_processing = False
        self.status_var.set("正在停止...")
        
    def clear_results(self):
        """清除結果"""
        self.result_text.delete(1.0, tk.END)
        self.export_btn.config(state="disabled")
        self.status_var.set("結果已清除")
        
    def export_results(self):
        """匯出結果"""
        if not hasattr(self, 'last_results') or not self.last_results:
            messagebox.showwarning("警告", "沒有可匯出的結果")
            return
        
        file_path = filedialog.asksaveasfilename(
            title="匯出結果",
            defaultextension=".json",
            filetypes=[("JSON 檔案", "*.json"), ("所有檔案", "*.*")]
        )
        
        if file_path:
            try:
                export_data = {
                    "timestamp": datetime.now().isoformat(),
                    "total_accounts": len(self.last_results),
                    "success_count": sum(1 for r in self.last_results if r['success']),
                    "error_count": sum(1 for r in self.last_results if not r['success']),
                    "results": self.last_results
                }
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, ensure_ascii=False, indent=2)
                
                messagebox.showinfo("成功", f"結果已匯出至: {file_path}")
                
            except Exception as e:
                messagebox.showerror("錯誤", f"匯出結果時發生錯誤: {str(e)}")


def main():
    """主程式入口點"""
    root = tk.Tk()
    LoginGUIApp(root)  # 建立應用程式實例
    root.mainloop()


if __name__ == "__main__":
    main()
