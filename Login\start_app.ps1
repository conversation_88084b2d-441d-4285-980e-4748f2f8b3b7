# 登入 API 測試工具啟動腳本
Write-Host "啟動登入 API 測試工具..." -ForegroundColor Green
Write-Host ""

# 切換到腳本所在目錄
Set-Location $PSScriptRoot

# 檢查虛擬環境是否存在
if (-not (Test-Path ".venv\Scripts\python.exe")) {
    Write-Host "錯誤: 找不到虛擬環境，請先執行以下命令建立:" -ForegroundColor Red
    Write-Host "python -m venv .venv" -ForegroundColor Yellow
    Write-Host "& .venv\Scripts\activate" -ForegroundColor Yellow
    Write-Host "pip install -r requirements.txt" -ForegroundColor Yellow
    Read-Host "按 Enter 鍵結束"
    exit 1
}

# 啟動應用程式
try {
    Write-Host "正在啟動 GUI 應用程式..." -ForegroundColor Cyan
    & .venv\Scripts\python.exe main.py
}
catch {
    Write-Host "啟動失敗: $($_.Exception.Message)" -ForegroundColor Red
    Read-Host "按 Enter 鍵結束"
}
