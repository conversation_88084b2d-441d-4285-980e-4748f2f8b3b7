# 登入 API 測試工具 - 使用說明

## 🎯 功能概述

這是一個 GUI 應用程式，可以：
- 匯入 Excel 檔案中的帳號資料
- 批次執行登入 API 請求
- 即時顯示執行結果
- 匯出結果為 JSON 格式

## 🚀 快速啟動

### 方法一：使用批次檔案（推薦）
雙擊 `start_app.bat` 檔案即可啟動

### 方法二：使用 PowerShell
雙擊 `start_app.ps1` 檔案

### 方法三：命令列啟動
```powershell
# 啟動虛擬環境
& .venv\Scripts\python.exe main.py
```

## 📋 使用步驟

### 1. 啟動應用程式
- 雙擊 `start_app.bat` 或使用其他啟動方式
- 等待 GUI 視窗開啟

### 2. 匯入 Excel 檔案
- 點擊「選擇檔案」按鈕
- 選擇包含帳號資料的 Excel 檔案
- 支援格式：`.xlsx`、`.xls`、`.csv`

### 3. 選擇資料來源
- **工作表**：如果 Excel 有多個工作表，選擇包含帳號的工作表
- **帳號欄位**：選擇包含帳號資料的欄位

### 4. 設定 API 參數
- **API URL**：預設為 `http://common:8080`
- **超時時間**：預設為 30 秒，可根據需要調整

### 5. 執行 API 請求
- 點擊「執行 API 請求」按鈕
- 觀察執行進度和結果
- 可隨時點擊「停止」按鈕中斷執行

### 6. 查看和匯出結果
- 在結果區域查看詳細執行記錄
- 點擊「匯出結果」將結果儲存為 JSON 檔案
- 點擊「清除結果」清空顯示區域

## 📊 範例資料

專案已包含範例檔案 `sample_accounts.xlsx`，包含：
- 10 個測試帳號
- 完整的欄位資訊（帳號、姓名、類型、狀態、備註）
- 可直接用於測試功能

## 🔧 API 規格

### 請求端點
```
POST http://common:8080/LoginService/OperateRequest/Login
```

### 請求格式
```json
{
  "gameId": "230001",
  "accountInfo": {
    "account": "從Excel讀取的帳號",
    "agentCode": "OCMS001",
    "agentType": 1,
    "subAgentCode": "OCMS001",
    "currency": "PHP"
  }
}
```

### 回應處理
- **成功**：HTTP 200 狀態碼
- **失敗**：其他 HTTP 狀態碼或網路錯誤

## 📁 檔案結構

```
Login/
├── main.py                 # 主程式入口
├── gui_app.py              # GUI 介面
├── api_client.py           # API 請求處理
├── excel_processor.py      # Excel 檔案處理
├── requirements.txt        # 依賴套件清單
├── sample_accounts.xlsx    # 範例資料檔案
├── start_app.bat          # Windows 批次啟動檔
├── start_app.ps1          # PowerShell 啟動腳本
├── test_functionality.py  # 功能測試腳本
├── README.md              # 完整技術文件
├── QUICK_START.md         # 快速開始指南
└── 使用說明.md            # 本檔案
```

## 🛠️ 故障排除

### 常見問題

1. **無法啟動應用程式**
   ```powershell
   # 檢查 Python 版本（需要 3.7+）
   python --version
   
   # 重新安裝依賴套件
   & .venv\Scripts\python.exe -m pip install -r requirements.txt
   ```

2. **檔案讀取失敗**
   - 確認檔案格式正確（.xlsx、.xls、.csv）
   - 檢查檔案是否被其他程式開啟
   - 確認檔案內容不為空

3. **API 連線失敗**
   - 檢查網路連線
   - 確認 API URL 正確
   - 調整超時時間設定
   - 檢查防火牆設定

4. **執行緩慢**
   - 減少一次處理的帳號數量
   - 增加超時時間
   - 檢查網路速度

### 測試功能
```powershell
# 執行完整功能測試
& .venv\Scripts\python.exe test_functionality.py
```

## 💡 使用技巧

1. **批次處理優化**
   - 建議一次處理不超過 100 個帳號
   - 大量資料可分批執行
   - 注意 API 服務器的負載限制

2. **資料準備**
   - Excel 檔案第一行應為欄位標題
   - 帳號欄位避免包含空白或特殊字元
   - 可使用 CSV 格式提高相容性

3. **結果分析**
   - 匯出的 JSON 檔案包含詳細的執行資訊
   - 可用於後續分析和報告
   - 包含時間戳記和統計資料

## 🔒 安全注意事項

- 不要在生產環境使用測試帳號
- 保護 API 端點和帳號資料安全
- 定期更新依賴套件
- 妥善保管執行結果檔案

## 📞 技術支援

如有問題或建議，請：
1. 先查看本說明文件
2. 執行功能測試確認環境
3. 檢查日誌檔案（`login_tool_*.log`）
4. 聯絡開發團隊

---

**版本資訊**：v1.0  
**最後更新**：2025-06-03  
**開發團隊**：內部開發
